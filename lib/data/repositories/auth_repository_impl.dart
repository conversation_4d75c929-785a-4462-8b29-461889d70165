import '../../domain/entities/auth_result.dart';
import '../../domain/entities/auth_user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_local_datasource.dart';
import '../datasources/auth_remote_datasource.dart';
import '../models/auth_session_model.dart';
import '../models/auth_user_model.dart';
import '../../view_model/custom_exception.dart';

/// Implementation of AuthRepository using clean architecture principles
/// 
/// This repository coordinates between remote and local data sources to provide
/// authentication functionality. It handles data transformation between the data
/// layer models and domain entities, manages token refresh, and provides
/// offline capabilities through local storage.
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;

  const AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<AuthResult> login(String email, String password) async {
    try {
      // Attempt remote login
      final loginResult = await remoteDataSource.login(email, password);
      
      if (!loginResult.success) {
        if (loginResult.twoFactorRefCode != null) {
          return AuthResult.twoFactorRequired(
            twoFactorRefCode: loginResult.twoFactorRefCode!,
          );
        } else {
          return AuthResult.failure(
            error: loginResult.error ?? 'Login failed',
          );
        }
      }

      // Login successful, save tokens and get user info
      final tokens = loginResult.tokens!;
      await localDataSource.saveTokens(
        tokens['access_token']!,
        tokens['refresh_token']!,
      );

      // Save credentials for biometric login if biometric is available
      if (await localDataSource.isBiometricAvailable()) {
        await localDataSource.saveCredentials(email, password);
      }

      // Get user information
      final userModel = await remoteDataSource.getUserInfo();
      await localDataSource.saveUser(userModel);

      // Create and save session
      final session = AuthSessionModel.fromTokens(
        accessToken: tokens['access_token']!,
        refreshToken: tokens['refresh_token']!,
        email: email,
        biometricEnabled: await localDataSource.isBiometricEnabled(),
      );
      await localDataSource.saveSession(session);

      return AuthResult.success(
        user: userModel.toDomain(),
        accessToken: tokens['access_token'],
        refreshToken: tokens['refresh_token'],
      );
    } catch (e) {
      return AuthResult.failure(
        error: e.toString().replaceAll('CustomException: ', ''),
      );
    }
  }

  @override
  Future<AuthResult> loginWithBiometric(String email) async {
    try {
      // Check if biometric is available and enabled
      if (!await localDataSource.isBiometricAvailable()) {
        return AuthResult.failure(error: 'Biometric authentication not available');
      }

      if (!await localDataSource.isBiometricEnabled()) {
        return AuthResult.failure(error: 'Biometric authentication not enabled');
      }

      // Get stored credentials
      final credentials = await localDataSource.getCredentials();
      if (credentials == null || credentials['email'] != email) {
        return AuthResult.failure(error: 'No stored credentials for this email');
      }

      // Authenticate with biometric
      final authenticated = await localDataSource.authenticateWithBiometric(
        reason: 'Please authenticate to login',
      );

      if (!authenticated) {
        return AuthResult.failure(error: 'Biometric authentication failed');
      }

      // Use stored credentials to login
      return await login(email, credentials['password']!);
    } catch (e) {
      return AuthResult.failure(
        error: 'Biometric authentication failed: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Attempt remote logout (don't fail if this fails)
      try {
        await remoteDataSource.logout();
      } catch (e) {
        // Log the error but continue with local logout
        print('Remote logout failed: $e');
      }

      // Clear all local data
      await localDataSource.clearAllData();
    } catch (e) {
      // Logout should always succeed locally
      print('Local logout error: $e');
    }
  }

  @override
  Future<void> globalLogout() async {
    try {
      // Perform remote logout first
      await remoteDataSource.logout();
      
      // Clear all local data
      await localDataSource.clearAllData();
    } catch (e) {
      // Even if remote logout fails, clear local data
      await localDataSource.clearAllData();
      throw Exception('Global logout failed: ${e.toString()}');
    }
  }

  @override
  Future<AuthResult> verifyTwoFactor(String refCode, String code) async {
    try {
      // Verify two-factor code with remote service
      final tokens = await remoteDataSource.verifyTwoFactor(refCode, code);
      
      // Save tokens
      await localDataSource.saveTokens(
        tokens['access_token']!,
        tokens['refresh_token']!,
      );

      // Get user information
      final userModel = await remoteDataSource.getUserInfo();
      await localDataSource.saveUser(userModel);

      // Update session with two-factor enabled
      final session = AuthSessionModel.fromTokens(
        accessToken: tokens['access_token']!,
        refreshToken: tokens['refresh_token']!,
        twoFactorEnabled: true,
        biometricEnabled: await localDataSource.isBiometricEnabled(),
      );
      await localDataSource.saveSession(session);
      await localDataSource.setTwoFactorEnabled(true);

      return AuthResult.success(
        user: userModel.toDomain(),
        accessToken: tokens['access_token'],
        refreshToken: tokens['refresh_token'],
      );
    } catch (e) {
      return AuthResult.failure(
        error: e.toString().replaceAll('CustomException: ', ''),
      );
    }
  }

  @override
  Future<String> refreshToken() async {
    try {
      // Get current refresh token
      final currentRefreshToken = await localDataSource.getRefreshToken();
      if (currentRefreshToken == null) {
        throw Exception('No refresh token available');
      }

      // Request new tokens
      final tokens = await remoteDataSource.refreshToken(currentRefreshToken);
      
      // Save new tokens
      await localDataSource.saveTokens(
        tokens['access_token']!,
        tokens['refresh_token']!,
      );

      // Update session
      final currentSession = await localDataSource.getSession();
      if (currentSession != null) {
        final updatedSession = currentSession.copyWithTokens(
          accessToken: tokens['access_token']!,
          refreshToken: tokens['refresh_token']!,
        );
        await localDataSource.saveSession(updatedSession);
      }

      return tokens['access_token']!;
    } catch (e) {
      throw Exception('Token refresh failed: ${e.toString()}');
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      // Check if we have a valid session
      final session = await localDataSource.getSession();
      if (session == null || !session.isValid) {
        return false;
      }

      // If token is expiring, try to refresh it
      if (session.isTokenExpiring) {
        try {
          await refreshToken();
          return true;
        } catch (e) {
          return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<AuthUser?> getCurrentUser() async {
    try {
      // Check if authenticated first
      if (!await isAuthenticated()) {
        return null;
      }

      // Try to get user from local storage first
      final userModel = await localDataSource.getUser();
      if (userModel != null) {
        return userModel.toDomain();
      }

      // If not available locally, fetch from remote
      final remoteUserModel = await remoteDataSource.getUserInfo();
      await localDataSource.saveUser(remoteUserModel);
      
      return remoteUserModel.toDomain();
    } catch (e) {
      return null;
    }
  }
}
